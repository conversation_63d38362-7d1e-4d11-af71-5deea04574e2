/* 加载组件基础样式 */
.loading {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: var(--loading-container-bg);
  padding: var(--spacing-sm) var(--spacing-lg);
  gap: var(--spacing-sm);
  border-radius: var(--radius-base);
  white-space: nowrap;
}

.loading--centered {
  width: 100%;
  height: 100%;
  min-height: var(--spacing-massive);
}

/* 加载动画 */
.loading__spinner {
  width: 16px;
  height: 16px;
  border-width: 2px;
  border-radius: 50%;
  border-style: solid;
  border-color: var(--color-content-mute);
  border-top-color: var(--color-content-accent);
  animation: loading-spin 1s linear infinite;
  flex-shrink: 0;
}

.loading__text {
  color: var(--color-content-accent);
  font-size: var(--font-size-base);
  line-height: 1;
  margin: 0;
}

/* 旋转动画 */
@keyframes loading-spin {
  0% { 
    transform: rotate(0deg); 
  }
  100% { 
    transform: rotate(360deg); 
  }
}
