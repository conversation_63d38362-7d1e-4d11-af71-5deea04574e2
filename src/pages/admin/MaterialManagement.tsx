import { useState, useEffect } from 'react';
import { SearchBox } from '../../components/search-box/search-box';
import { PrimaryButton } from '../../components/primary-button/primary-button';
import { IconButton } from '../../components/icon-button/icon-button';
import { CustomMaterialPanel } from '../../components/custom-material-panel/custom-material-panel';
import { Loading } from '../../components/loading/loading';
import { Modal } from '../../components/modal/modal';
import type { MaterialSettings } from '../../components/custom-material-panel/custom-material-panel';
import { Trash, Edit, Palette } from 'lucide-react';
import MaterialPreview from '../../components/material-preview/MaterialPreview';
import './AdminTable.css';

import { apiService } from '../../services/api';
import type { MaterialData } from '../../services/api';
import MaterialThumbnailSimple from '../../components/material-thumbnail/MaterialThumbnailSimple';

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const MaterialManagement = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [selectedMaterial, setSelectedMaterial] = useState<MaterialData | null>(null);
  const [loading, setLoading] = useState(false);
  const [materials, setMaterials] = useState<MaterialData[]>([]);

  useEffect(() => {
    const loadMaterials = async () => {
      setLoading(true);
      try {
        const data = await apiService.getMaterials();
        setMaterials(data);
      } catch (error) {
        console.error('加载材质失败:', error);
      } finally {
        setLoading(false);
      }
    };
    loadMaterials();
  }, []);

  const deleteMaterial = async (id: string) => {
    if (window.confirm('确定要删除这个材质吗？')) {
      try {
        await apiService.deleteMaterial(id);
        setMaterials(materials.filter(material => material.id !== id));
      } catch (error) {
        console.error('删除材质失败:', error);
      }
    }
  };

  const handleSaveMaterial = async (materialData: Partial<Omit<MaterialData, 'id' | 'createdAt'>>) => {
    try {
      let savedMaterial: MaterialData;
      if (selectedMaterial) {
        savedMaterial = await apiService.updateMaterial(selectedMaterial.id, materialData);
        setMaterials(materials.map(m => m.id === savedMaterial.id ? savedMaterial : m));
      } else {
        savedMaterial = await apiService.createMaterial(materialData);
        setMaterials([...materials, savedMaterial]);
      }
      setShowAddModal(false);
      setSelectedMaterial(null);
    } catch (error) {
      console.error('保存材质失败:', error);
      alert(`保存材质失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  const handleOpenAddModal = () => {
    setSelectedMaterial(null);
    setShowAddModal(true);
  };

  const handleCloseModal = () => {
    setShowAddModal(false);
    setSelectedMaterial(null);
  };

  const filteredMaterials = materials.filter(material => 
    material.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (loading) {
    return <Loading text="正在加载材质列表..." variant="minimal" />;
  }

  return (
    <div className="material-management">
      <div className="management-toolbar">
        <SearchBox 
          placeholder="搜索材质" 
          value={searchQuery} 
          onChange={setSearchQuery}
          width={300}
        />
        <div className="toolbar-actions">
          <PrimaryButton onClick={handleOpenAddModal}>
            添加材质
          </PrimaryButton>
        </div>
      </div>
      
      <div className="admin-table-container">
        <table className="admin-table">
          <thead>
            <tr>
              <th>缩略图</th>
              <th>材质名称</th>
              <th>颜色</th>
              <th>金属感</th>
              <th>粗糙度</th>
              <th>不透明度</th>
              <th>创建时间</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            {filteredMaterials.map(material => (
              <tr key={material.id} className="material-row">
                <td className="thumbnail-cell">
                  <MaterialThumbnailSimple material={material} size={44} />
                </td>
                <td className="material-name">{material.name}</td>
                <td className="material-color">
                  <div
                    className="color-preview"
                    style={{ '--preview-color': material.color } as React.CSSProperties}
                    title={material.color}
                  ></div>
                </td>
                <td>{material.metalness}%</td>
                <td>{material.roughness}%</td>
                <td>{100 - material.glass}%</td>
                <td className="date-cell">{formatDate(material.createdAt)}</td>
                <td className="actions-cell">
                  <IconButton icon={Edit} size="small" onClick={() => setSelectedMaterial(material)} />
                  <IconButton icon={Trash} size="small" onClick={() => deleteMaterial(material.id)} />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      {(showAddModal || selectedMaterial) && (
        <MaterialModal 
          key={selectedMaterial?.id || 'new'}
          material={selectedMaterial || undefined}
          onClose={handleCloseModal} 
          onSave={handleSaveMaterial}
        />
      )}
    </div>
  );
};

interface MaterialModalProps {
  material?: MaterialData;
  onClose: () => void;
  onSave: (data: Partial<Omit<MaterialData, 'id' | 'createdAt'>>) => void | Promise<void>;
}

const MaterialModal = ({ material, onClose, onSave }: MaterialModalProps) => {
  const [name, setName] = useState(material?.name || '');
  const [materialSettings, setMaterialSettings] = useState<MaterialSettings>({
    color: material?.color || '#B39B9C',
    metalness: material ? material.metalness / 100 : 0.5,
    roughness: material ? material.roughness / 100 : 0.5,
    opacity: material && typeof material.glass === 'number' ? (100 - material.glass) / 100 : 1,
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave({ 
      name,
      color: materialSettings.color,
      metalness: Math.round(materialSettings.metalness * 100),
      roughness: Math.round(materialSettings.roughness * 100),
      glass: Math.round((1 - materialSettings.opacity) * 100),
    });
  };
  
  return (
    <Modal
      visible={true}
      title={material ? '编辑材质' : '添加新材质'}
      onClose={onClose}
      size="medium"
    >
      <form className="modal-form" onSubmit={handleSubmit}>
        <fieldset className="form-fieldset">
          <div className="form-row-flex">
            <div className="material-preview-container">
              <div className="preview-header">
                <Palette size={16} />
                <span>材质预览</span>
              </div>
              <div className="preview-sphere">
                <MaterialPreview settings={materialSettings} size={100} />
              </div>
            </div>
            <div className="form-group" style={{ flex: 1 }}>
              <label>材质名称</label>
              <input
                type="text"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="输入材质名称"
                required
                className="form-input"
              />
            </div>
          </div>

          <div className="material-settings">
            <CustomMaterialPanel
              defaultSettings={materialSettings}
              onChange={(settings) => {
                setMaterialSettings(settings as MaterialSettings);
              }}
            />
          </div>
        </fieldset>

        <div className="modal-actions">
          <PrimaryButton type="submit" showIcon={false}>
            保存
          </PrimaryButton>
        </div>
      </form>
    </Modal>
  );
};

export default MaterialManagement;