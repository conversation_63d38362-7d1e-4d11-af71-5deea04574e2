#!/usr/bin/env node

/**
 * 环境检查脚本
 * 检查开发环境的完整性和配置
 */

import { platform, arch } from 'os';
import { existsSync, readFileSync } from 'fs';
import { execSync } from 'child_process';
import { createRequire } from 'module';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config({ path: '.env.local' });
dotenv.config({ path: '.env' });

const require = createRequire(import.meta.url);

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkMark(condition) {
  return condition ? '✅' : '❌';
}

// 检查系统信息
function checkSystemInfo() {
  log('\n🖥️  System Information', 'cyan');
  log(`   Platform: ${platform()} (${arch()})`, 'blue');
  log(`   Node.js: ${process.version}`, 'blue');
  
  try {
    const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
    log(`   npm: v${npmVersion}`, 'blue');
  } catch {
    log('   npm: Not available', 'red');
  }
}

// 检查环境变量
function checkEnvironmentVariables() {
  log('\n🔧 Environment Variables', 'cyan');
  
  const requiredVars = [
    'POSTGRES_PRISMA_URL',
    'BLOB_READ_WRITE_TOKEN'
  ];
  
  const optionalVars = [
    'POSTGRES_URL_NON_POOLING',
    'NODE_ENV',
    'PORT'
  ];
  
  let allRequired = true;
  
  // 检查必需的环境变量
  log('   Required:', 'yellow');
  for (const varName of requiredVars) {
    const exists = !!process.env[varName];
    log(`   ${checkMark(exists)} ${varName}`, exists ? 'green' : 'red');
    if (!exists) allRequired = false;
  }
  
  // 检查可选的环境变量
  log('   Optional:', 'yellow');
  for (const varName of optionalVars) {
    const exists = !!process.env[varName];
    const value = exists ? (varName === 'NODE_ENV' ? process.env[varName] : 'Set') : 'Not set';
    log(`   ${checkMark(exists)} ${varName}: ${value}`, exists ? 'green' : 'yellow');
  }
  
  return allRequired;
}

// 检查配置文件
function checkConfigFiles() {
  log('\n📄 Configuration Files', 'cyan');
  
  const configFiles = [
    { path: '.env', required: false },
    { path: '.env.local', required: false },
    { path: 'package.json', required: true },
    { path: 'vite.config.ts', required: true },
    { path: 'tsconfig.json', required: true },
    { path: 'prisma/schema.prisma', required: true }
  ];
  
  let allRequired = true;
  
  for (const { path, required } of configFiles) {
    const exists = existsSync(path);
    const status = required ? (exists ? 'green' : 'red') : (exists ? 'green' : 'yellow');
    const label = required ? 'Required' : 'Optional';
    
    log(`   ${checkMark(exists)} ${path} (${label})`, status);
    
    if (required && !exists) {
      allRequired = false;
    }
  }
  
  return allRequired;
}

// 检查依赖项
function checkDependencies() {
  log('\n📦 Dependencies', 'cyan');
  
  try {
    const packageJson = JSON.parse(readFileSync('package.json', 'utf8'));
    const nodeModulesExists = existsSync('node_modules');
    
    log(`   ${checkMark(nodeModulesExists)} node_modules directory`, nodeModulesExists ? 'green' : 'red');
    
    if (nodeModulesExists) {
      try {
        execSync('npm list --depth=0', { stdio: 'pipe' });
        log('   ✅ All dependencies installed', 'green');
      } catch {
        log('   ⚠️  Some dependencies may be missing', 'yellow');
      }
    }
    
    // 检查关键依赖
    const criticalDeps = ['react', 'vite', 'express', '@prisma/client'];
    log('   Critical dependencies:', 'yellow');
    
    for (const dep of criticalDeps) {
      const exists = packageJson.dependencies?.[dep] || packageJson.devDependencies?.[dep];
      log(`   ${checkMark(exists)} ${dep}`, exists ? 'green' : 'red');
    }
    
    return nodeModulesExists;
  } catch (error) {
    log(`   ❌ Error reading package.json: ${error.message}`, 'red');
    return false;
  }
}

// 检查端口可用性
function checkPorts() {
  log('\n🔌 Port Availability', 'cyan');
  
  const ports = [3001, 5173, 5174, 5175, 5176];
  
  for (const port of ports) {
    try {
      const isWindows = platform() === 'win32';
      const command = isWindows 
        ? `netstat -ano | findstr :${port}`
        : `lsof -ti:${port}`;
      
      execSync(command, { stdio: 'pipe' });
      log(`   ⚠️  Port ${port} is in use`, 'yellow');
    } catch {
      log(`   ✅ Port ${port} is available`, 'green');
    }
  }
}

// 检查数据库连接
async function checkDatabaseConnection() {
  log('\n🗄️  Database Connection', 'cyan');
  
  if (!process.env.POSTGRES_PRISMA_URL) {
    log('   ❌ POSTGRES_PRISMA_URL not configured', 'red');
    return false;
  }
  
  try {
    // 尝试导入和初始化Prisma客户端
    const { PrismaClient } = await import('@prisma/client');
    const prisma = new PrismaClient();
    
    // 尝试连接数据库
    await prisma.$connect();
    log('   ✅ Database connection successful', 'green');
    
    await prisma.$disconnect();
    return true;
  } catch (error) {
    log(`   ❌ Database connection failed: ${error.message}`, 'red');
    return false;
  }
}

// 生成诊断报告
function generateReport(checks) {
  log('\n📊 Diagnostic Report', 'cyan');
  
  const passed = Object.values(checks).filter(Boolean).length;
  const total = Object.keys(checks).length;
  const percentage = Math.round((passed / total) * 100);
  
  log(`   Overall Health: ${percentage}%`, percentage >= 80 ? 'green' : percentage >= 60 ? 'yellow' : 'red');
  log(`   Checks Passed: ${passed}/${total}`, 'blue');
  
  if (percentage < 100) {
    log('\n💡 Recommendations:', 'yellow');
    
    if (!checks.envVars) {
      log('   • Configure missing environment variables in .env or .env.local', 'yellow');
    }
    
    if (!checks.configFiles) {
      log('   • Ensure all required configuration files are present', 'yellow');
    }
    
    if (!checks.dependencies) {
      log('   • Run "npm install" to install missing dependencies', 'yellow');
    }
    
    if (!checks.database) {
      log('   • Check database configuration and network connectivity', 'yellow');
    }
  }
}

// 主函数
async function main() {
  log('🔍 Huitong Material Environment Diagnostic', 'bright');
  log('=' .repeat(50), 'blue');
  
  const checks = {
    envVars: checkEnvironmentVariables(),
    configFiles: checkConfigFiles(),
    dependencies: checkDependencies(),
    database: await checkDatabaseConnection()
  };
  
  checkSystemInfo();
  checkPorts();
  generateReport(checks);
  
  log('\n' + '='.repeat(50), 'blue');
  log('✅ Diagnostic complete', 'green');
  
  // 退出码
  const allPassed = Object.values(checks).every(Boolean);
  process.exit(allPassed ? 0 : 1);
}

// 运行诊断
main().catch(error => {
  log(`❌ Diagnostic failed: ${error.message}`, 'red');
  process.exit(1);
});
