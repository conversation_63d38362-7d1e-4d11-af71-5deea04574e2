import dotenv from 'dotenv';
// 优先加载 .env.local，如果不存在则加载 .env
dotenv.config({ path: '.env.local' });
dotenv.config({ path: '.env' });

import express from 'express';
import cors from 'cors';
import { PrismaClient } from '@prisma/client';
import { del as vercelBlobDel } from '@vercel/blob';
import { handleUpload } from '@vercel/blob/client';

const app = express();

const getDbUrl = () => {
  const isDevelopment = process.env.NODE_ENV === 'development';
  const dbUrl = process.env.POSTGRES_PRISMA_URL;

  // When connecting from a local environment to Vercel's pooled database,
  // we need to add specific parameters for the connection pooler (pgbouncer) and SSL.
  if (isDevelopment && dbUrl && dbUrl.includes('vercel-storage.com')) {
    try {
      const url = new URL(dbUrl);
      if (!url.searchParams.has('pgbouncer')) {
        url.searchParams.set('pgbouncer', 'true');
      }
      if (!url.searchParams.has('sslmode')) {
        url.searchParams.set('sslmode', 'require');
      }
      return url.toString();
    } catch (e) {
      console.error("Invalid POSTGRES_PRISMA_URL:", e);
      return dbUrl; // fallback to original url
    }
  }
  return dbUrl;
};

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: getDbUrl(),
    },
  },
});

app.use(cors());
app.use(express.json());

// --- API Routes ---

// This new endpoint will handle generating a signed URL for client-side uploads.
app.post('/api/upload', async (req, res) => {
  try {
    const jsonResponse = await handleUpload({
      body: req.body,
      request: req,
      onBeforeGenerateToken: async (pathname /*, clientPayload */) => {
        // Sanitize the pathname to remove special characters and spaces,
        // ensuring the generated URL is valid and accessible.
        const sanitizedPathname = pathname.replace(/[^a-zA-Z0-9_.-]/g, '_');
        // The token is read from the BLOB_READ_WRITE_TOKEN environment variable that
        // is automatically set by Vercel when a Blob store is connected.
        return {
          // The pathname property will override the initial pathname received
          pathname: sanitizedPathname,
          // Can add metadata to the token payload, like a user ID
        };
      },
      onUploadCompleted: async ({ blob, tokenPayload }) => {
        // This callback is executed after the file is uploaded to Vercel Blob.
        console.log('Blob upload completed!', blob, tokenPayload);
      },
    });

    res.status(200).json(jsonResponse);
  } catch (error) {
    console.error('An error occurred during upload handling:', error);
    res.status(400).json({ error: error.message });
  }
});

// GET all models
app.get('/api/models', async (req, res) => {
    try {
        const models = await prisma.model.findMany({
            orderBy: { createdAt: 'desc' },
        });
        res.json(models);
    } catch (error) {
        console.error('Failed to fetch models:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// GET a single model by ID
app.get('/api/models/:id', async (req, res) => {
    try {
        const model = await prisma.model.findUnique({ where: { id: req.params.id } });
        if (!model) return res.status(404).json({ error: 'Model not found' });
        res.json(model);
    } catch (error) {
        console.error(`Failed to fetch model ${req.params.id}:`, error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// POST a new model - now expects URLs from client-side upload
app.post('/api/models', async (req, res) => {
    const { name, fileType, size, url, thumbnailUrl } = req.body;

    if (!name || !url) {
        return res.status(400).json({ error: 'Name and model URL are required.' });
    }

    try {
        const newModel = await prisma.model.create({
            data: {
                name,
                fileType,
                size: String(size),
                filePath: url,
                url: url,
                thumbnail: thumbnailUrl,
            },
        });
        res.status(201).json(newModel);
    } catch (error) {
        console.error('Failed to create model record:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// DELETE a model
app.delete('/api/models/:id', async (req, res) => {
    try {
        const model = await prisma.model.findUnique({ where: { id: req.params.id } });
        if (!model) return res.status(404).json({ error: 'Model not found' });

        // Delete files from Vercel Blob storage
        if (model.url) await vercelBlobDel(model.url);
        if (model.thumbnail) await vercelBlobDel(model.thumbnail);

        // Delete from database
        await prisma.model.delete({ where: { id: req.params.id } });

        res.status(204).send();
    } catch (error) {
        console.error(`Failed to delete model ${req.params.id}:`, error);
        res.status(500).json({ error: 'Internal server error' });
    }
});


// GET all materials
app.get('/api/materials', async (req, res) => {
    try {
        const materials = await prisma.material.findMany({
            orderBy: { createdAt: 'desc' },
        });
        res.json(materials);
    } catch (error) {
        console.error('Failed to fetch materials:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// POST a new material - now expects thumbnail URL from client-side upload
app.post('/api/materials', async (req, res) => {
    const { name, color, metalness, roughness, glass, thumbnailUrl } = req.body;

    try {
        const newMaterial = await prisma.material.create({
            data: {
                name,
                color,
                metalness: parseFloat(metalness) || 0,
                roughness: parseFloat(roughness) || 0,
                glass: parseFloat(glass) || 0,
                thumbnail: thumbnailUrl,
            },
        });
        res.status(201).json(newMaterial);
    } catch (error) {
        console.error('Failed to add material:', error);
        res.status(500).json({ error: 'Failed to add material.' });
    }
});

// DELETE a material
app.delete('/api/materials/:id', async (req, res) => {
    try {
        const material = await prisma.material.findUnique({ where: { id: req.params.id } });
        if (material && material.thumbnail) {
            await vercelBlobDel(material.thumbnail);
        }
        await prisma.material.delete({ where: { id: req.params.id } });
        res.status(204).send();
    } catch (error) {
        console.error(`Failed to delete material ${req.params.id}:`, error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// --- Export and Listen ---

// This allows Vercel to import the app as a serverless function.
export default app;

// This block runs only when not in a Vercel environment (i.e., for Aliyun or local dev)
// This block runs for local development or any non-Vercel production environment.
// When running with `npm run dev`, NODE_ENV is 'development', ensuring the server starts.
if (process.env.NODE_ENV === 'development' || !process.env.VERCEL) {
    const PORT = process.env.PORT || 3001;

    const server = app.listen(PORT, () => {
        console.log(`🚀 Backend server is running on http://localhost:${PORT}`);
        console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
        console.log(`🗄️  Database: ${process.env.POSTGRES_PRISMA_URL ? 'Connected' : 'Not configured'}`);
    });

    // 优雅关闭处理
    process.on('SIGTERM', () => {
        console.log('🛑 SIGTERM received, shutting down gracefully');
        server.close(() => {
            console.log('✅ Process terminated');
            process.exit(0);
        });
    });

    process.on('SIGINT', () => {
        console.log('🛑 SIGINT received, shutting down gracefully');
        server.close(() => {
            console.log('✅ Process terminated');
            process.exit(0);
        });
    });

    // 错误处理
    server.on('error', (err) => {
        if (err.code === 'EADDRINUSE') {
            console.error(`❌ Port ${PORT} is already in use. Please try a different port or kill the process using this port.`);
            console.error(`💡 You can kill the process with: lsof -ti:${PORT} | xargs kill -9`);
            process.exit(1);
        } else {
            console.error('❌ Server error:', err);
            process.exit(1);
        }
    });
}
